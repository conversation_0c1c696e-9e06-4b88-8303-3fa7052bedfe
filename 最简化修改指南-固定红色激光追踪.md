# 最简化修改指南：绿色激光追踪固定红色激光

## 🎯 任务目标
用最少的修改，将"绿色激光追踪移动的红色激光"改为"绿色激光追踪固定不动的红色激光"

## 📍 核心问题分析

**当前代码逻辑**：
```c
// 在 pi_proc() 函数中
pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
```

**问题**：`latest_red_laser_coord.x` 和 `latest_red_laser_coord.y` 会随着摄像头检测到的红色激光位置实时变化

**解决方案**：将变化的红色激光坐标替换为固定的数值

## 🛠️ 最简化修改方案（只需修改1个文件的2行代码）

### 唯一需要修改的文件：`bsp/pi_bsp.c`

**找到这个函数**：
```c
void pi_proc(void)
{
    float pos_out_x,pos_out_y=0;

    pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
    Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);
}
```

**修改这2行代码**：
```c
void pi_proc(void)
{
    float pos_out_x,pos_out_y=0;

    // 修改前：latest_red_laser_coord.x (会变化的红色激光X坐标)
    // 修改后：320 (固定的目标X坐标)
    pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, 320, 0);
    
    // 修改前：latest_red_laser_coord.y (会变化的红色激光Y坐标)  
    // 修改后：240 (固定的目标Y坐标)
    pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, 240, 0);
    
    Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);
}
```

## 📝 详细操作步骤（小白版）

### 第1步：打开文件
1. 在您的工程中找到 `bsp` 文件夹
2. 打开 `pi_bsp.c` 文件

### 第2步：找到要修改的函数
1. 在文件中搜索 `void pi_proc(void)`
2. 找到这个函数（大约在第58行）

### 第3步：修改代码
**原始代码**：
```c
pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
```

**修改为**：
```c
pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, 320, 0);
pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, 240, 0);
```

**具体操作**：
- 将 `latest_red_laser_coord.x` 改为 `320`
- 将 `latest_red_laser_coord.y` 改为 `240`

### 第4步：保存并编译
1. 保存文件 (Ctrl+S)
2. 编译工程
3. 下载到STM32

## 🔢 坐标数值说明

**为什么选择 320 和 240？**
- 这是假设摄像头分辨率为 640×480 的屏幕中心点
- 320 = 640 ÷ 2 (水平中心)
- 240 = 480 ÷ 2 (垂直中心)

**如果您的摄像头分辨率不同**：
- 640×480 → 使用 (320, 240)
- 800×600 → 使用 (400, 300)  
- 1280×720 → 使用 (640, 360)
- 自定义 → 使用 (宽度÷2, 高度÷2)

**如果您想要其他固定位置**：
- 左上角 → (100, 100)
- 右下角 → (500, 350) [假设640×480]
- 任意位置 → (您想要的X, 您想要的Y)

## ✅ 修改完成后的效果

**修改前**：
- 绿色激光会跟随红色激光移动
- 红色激光移动到哪里，绿色激光就跟到哪里

**修改后**：
- 绿色激光会移动到固定位置 (320, 240) 并停留
- 不管红色激光在哪里，绿色激光都只去固定位置

## 🧪 测试验证

### 预期行为
1. 启动系统后，绿色激光应该移动到屏幕中心位置 (320, 240)
2. 到达目标位置后，绿色激光应该停止移动
3. 即使红色激光在其他位置，绿色激光也不会跟随

### 如果效果不理想
**问题1：绿色激光不移动**
- 检查是否有绿色激光数据输入
- 检查串口连接是否正常

**问题2：绿色激光移动但不到指定位置**
- 检查坐标值是否在摄像头视野范围内
- 尝试修改为其他坐标值，如 (200, 200)

**问题3：到达位置后有震荡**
- 这是正常现象，PID控制器在微调位置
- 如需消除震荡，需要调整PID参数（这是高级操作）

## 🔄 如果需要改回原来的功能

只需要将修改的2行代码改回去：
```c
pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
```

## 📋 完整的修改后代码

```c
void pi_proc(void)
{
    float pos_out_x,pos_out_y=0;

    // 绿色激光追踪固定位置 (320, 240)
    pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, 320, 0);
    pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, 240, 0);
    Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);
}
```

---

## 🎉 总结

**修改内容**：只需修改1个文件的2个数字
**修改文件**：`bsp/pi_bsp.c`
**修改位置**：`pi_proc()` 函数中的2行代码
**修改内容**：将 `latest_red_laser_coord.x` 改为 `320`，将 `latest_red_laser_coord.y` 改为 `240`

这是最简单、最直接的修改方案，保持了所有原有的函数名和代码结构！

---

**版权声明**: 本文档归属于【米醋电子工作室】
**文档版本**: v1.0
**最后更新**: 2025年1月
