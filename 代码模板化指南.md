# STM32F407 电赛项目代码模板化指南

## 项目概述

本项目是基于STM32F407VET6的电子设计竞赛模板代码，主要实现了双轴步进电机控制系统，具备激光跟踪、PID控制、串口通信等功能。

## 项目架构分析

### 1. 目录结构
```
├── Core/                    # STM32CubeMX生成的核心文件
├── Drivers/                 # HAL库驱动
├── OLED/                   # OLED显示屏驱动
├── TB6612/                 # TB6612电机驱动模块
├── app/                    # 应用层驱动
├── bsp/                    # 板级支持包
├── ringbuffer/             # 环形缓冲区
└── MDK-ARM/                # Keil工程文件
```

### 2. 核心功能模块
- **步进电机控制**: 双轴(X/Y)步进电机精确控制
- **激光跟踪**: 红绿激光点坐标识别与跟踪
- **PID控制**: 位置闭环控制算法
- **串口通信**: 多路串口数据处理
- **任务调度**: 基于时间片的任务调度系统

## 需要修改的关键配置点

### 1. 硬件配置修改 (高优先级)

#### 1.1 MCU型号配置
**文件**: `2025template.ioc`
**修改点**:
```
Mcu.CPN=STM32F407VET6  # 根据实际MCU型号修改
```

#### 1.2 串口配置
**文件**: `bsp/step_motor_bsp.h`
**修改点**:
```c
#define MOTOR_X_UART        huart2        // X轴电机串口
#define MOTOR_Y_UART        huart4        // Y轴电机串口
```
**说明**: 根据实际硬件连接修改串口号

#### 1.3 电机地址配置
**文件**: `bsp/step_motor_bsp.h`
**修改点**:
```c
#define MOTOR_X_ADDR        0x01          // X轴电机地址
#define MOTOR_Y_ADDR        0x01          // Y轴电机地址
```

#### 1.4 电机参数配置
**文件**: `bsp/step_motor_bsp.h`
**修改点**:
```c
#define MOTOR_MAX_SPEED     3            // 最大转速(RPM)
#define MOTOR_ACCEL         0             // 加速度(0表示直接启动)
#define MOTOR_MAX_ANGLE     50            // 最大角度限制(±50°)
```

### 2. 功能模块配置 (中优先级)

#### 2.1 PID参数调整
**文件**: `app/mypid.c`
**修改点**:
```c
// PID_INIT()函数中的参数
PID_struct_init(&pid_x ,POSITION_PID , 3, 1, 0.02, 0, 0);  // X轴PID参数
PID_struct_init(&pid_y ,POSITION_PID , 3, 1, 0.02, 0, 0);  // Y轴PID参数
```
**参数说明**: (模式, Kp, Ki, Kd, 最大输出, 积分限制)

#### 2.2 激光识别协议
**文件**: `bsp/pi_bsp.c`
**修改点**:
```c
// pi_parse_data()函数中的协议格式
"red:(x,y)"  // 红色激光坐标格式
"gre:(x,y)"  // 绿色激光坐标格式
```

#### 2.3 任务调度配置
**文件**: `bsp/schedule.c`
**修改点**:
```c
static schedule_task_t schedule_task[] = {
    {uart_proc,1,0},     // 串口处理任务，1ms周期
    {pi_proc,20,0}       // 激光处理任务，20ms周期
    // 可添加更多任务
};
```

### 3. 通信协议修改 (中优先级)

#### 3.1 串口波特率
**文件**: 在STM32CubeMX中配置或Core/Src/usart.c
**修改点**: 根据外设要求修改各串口波特率

#### 3.2 环形缓冲区大小
**文件**: `bsp/uart_bsp.c`
**修改点**:
```c
uint8_t ringbuffer_pool_x[64];   // X轴电机数据缓冲区
uint8_t ringbuffer_pool_y[64];   // Y轴电机数据缓冲区
uint8_t ringbuffer_pool_pi[64];  // 视觉数据缓冲区
```

### 4. 算法参数优化 (低优先级)

#### 4.1 死区设置
**文件**: `app/mypid.c`
**修改点**:
```c
// 在PID_INIT()中添加死区设置
pid_x.input_deadband = 5;   // 输入死区
pid_x.output_deadband = 1;  // 输出死区
```

#### 4.2 滤波参数
**文件**: `app/mypid.c`
**修改点**:
```c
// pid_calc()函数中的滤波系数
pid->err[NOW] = raw_error * 0.7f + pid->err[LAST] * 0.3f;
```

## 项目定制化步骤

### 第一步: 硬件适配
1. 修改MCU型号和引脚配置
2. 调整串口分配和波特率
3. 确认电机地址和通信协议

### 第二步: 功能裁剪
1. 根据需求启用/禁用功能模块
2. 修改任务调度表
3. 调整缓冲区大小

### 第三步: 参数调优
1. 调整PID控制参数
2. 设置运动限制参数
3. 优化通信协议

### 第四步: 测试验证
1. 单元功能测试
2. 系统集成测试
3. 性能优化调整

## 重要注意事项

### 1. 安全限制
- 电机角度限制功能默认关闭，需要时在uart_bsp.c中启用
- 最大速度和角度限制需根据机械结构设置

### 2. 实时性要求
- 串口处理任务周期为1ms，确保数据及时处理
- PID控制周期为20ms，可根据控制精度要求调整

### 3. 内存管理
- 环形缓冲区大小需根据数据量调整
- 注意栈溢出问题，特别是在中断服务程序中

### 4. 调试支持
- 通过UART1输出调试信息
- 可通过my_printf函数添加调试输出

## 扩展建议

### 1. 功能扩展
- 添加编码器反馈
- 集成IMU姿态传感器
- 增加OLED状态显示

### 2. 性能优化
- 实现自适应PID参数
- 添加前馈控制
- 优化通信效率

### 3. 安全增强
- 添加故障检测机制
- 实现紧急停止功能
- 增加参数合法性检查

## 详细配置示例

### 1. 主函数初始化修改示例
**文件**: `Core/Src/main.c`
**原始代码**:
```c
/* USER CODE BEGIN 2 */
schedule_init();
PID_INIT();
rt_ringbuffer_init(&ringbuffer_y, ringbuffer_pool_y,  sizeof(ringbuffer_pool_y));
rt_ringbuffer_init(&ringbuffer_x, ringbuffer_pool_x,  sizeof(ringbuffer_pool_x));
rt_ringbuffer_init(&ringbuffer_pi,ringbuffer_pool_pi, sizeof(ringbuffer_pool_pi));
Step_Motor_Init();
Emm_V5_Reset_CurPos_To_Zero(&huart4, 0x01);
Emm_V5_Reset_CurPos_To_Zero(&huart2, 0x01);
save_initial_position();
/* USER CODE END 2 */
```

**修改建议**:
```c
/* USER CODE BEGIN 2 */
// 1. 任务调度器初始化
schedule_init();

// 2. PID控制器初始化 - 根据实际需求调整参数
PID_INIT();

// 3. 环形缓冲区初始化 - 根据数据量调整大小
rt_ringbuffer_init(&ringbuffer_y, ringbuffer_pool_y,  sizeof(ringbuffer_pool_y));
rt_ringbuffer_init(&ringbuffer_x, ringbuffer_pool_x,  sizeof(ringbuffer_pool_x));
rt_ringbuffer_init(&ringbuffer_pi,ringbuffer_pool_pi, sizeof(ringbuffer_pool_pi));

// 4. 步进电机初始化 - 检查串口和地址配置
Step_Motor_Init();

// 5. 电机位置复位 - 确认电机地址正确
Emm_V5_Reset_CurPos_To_Zero(&MOTOR_Y_UART, MOTOR_Y_ADDR);
Emm_V5_Reset_CurPos_To_Zero(&MOTOR_X_UART, MOTOR_X_ADDR);

// 6. 保存初始位置
save_initial_position();

// 7. 可选：启用其他功能模块
// encoder_init();    // 编码器初始化
// motor_init();      // 直流电机初始化
// OLED_Init();       // OLED显示初始化
/* USER CODE END 2 */
```

### 2. 系统头文件配置示例
**文件**: `bsp/bsp_system.h`
**修改说明**: 根据实际使用的模块添加或删除头文件包含

```c
// 核心系统头文件 - 必需
#include "main.h"
#include "dma.h"
#include "usart.h"
#include "tim.h"
#include "i2c.h"

// 功能模块头文件 - 根据需求选择
#include "schedule.h"        // 任务调度 - 必需
#include "ringbuffer.h"      // 环形缓冲区 - 必需

// 硬件驱动头文件 - 根据硬件配置选择
#include "oled.h"           // OLED显示 - 可选
#include "hardware_iic.h"   // 硬件I2C - 可选

// BSP层头文件 - 根据功能需求选择
#include "uart_bsp.h"       // 串口处理 - 必需
#include "step_motor_bsp.h" // 步进电机 - 必需
#include "pi_bsp.h"         // 视觉处理 - 根据需求
#include "oled_bsp.h"       // OLED接口 - 可选
#include "key_bsp.h"        // 按键处理 - 可选
#include "encoder_bsp.h"    // 编码器 - 可选
#include "motor_bsp.h"      // 直流电机 - 可选
#include "hwt101_bsp.h"     // IMU传感器 - 可选
#include "gray_bsp.h"       // 灰度传感器 - 可选

// 应用层头文件
#include "Emm_V5.h"         // 步进电机驱动 - 必需
#include "mypid.h"          // PID控制 - 必需
#include "motor_driver.h"   // 电机驱动 - 可选
#include "encoder_drv.h"    // 编码器驱动 - 可选
#include "hwt101_driver.h"  // IMU驱动 - 可选
#include "motor_driver_tb6612.h" // TB6612驱动 - 可选
```

### 3. 不同应用场景的配置模板

#### 3.1 激光跟踪云台配置
```c
// step_motor_bsp.h 配置
#define MOTOR_X_UART        huart2        // 水平轴
#define MOTOR_Y_UART        huart4        // 俯仰轴
#define MOTOR_MAX_SPEED     5             // 较高速度
#define MOTOR_MAX_ANGLE     90            // 大角度范围

// mypid.c PID参数
PID_struct_init(&pid_x, POSITION_PID, 5, 2, 0.05, 0, 0);   // 快速响应
PID_struct_init(&pid_y, POSITION_PID, 5, 2, 0.05, 0, 0);
```

#### 3.2 精密定位平台配置
```c
// step_motor_bsp.h 配置
#define MOTOR_X_UART        huart2
#define MOTOR_Y_UART        huart4
#define MOTOR_MAX_SPEED     1             // 低速高精度
#define MOTOR_MAX_ANGLE     30            // 小角度范围

// mypid.c PID参数
PID_struct_init(&pid_x, POSITION_PID, 2, 0.5, 0.01, 0, 0); // 高精度
PID_struct_init(&pid_y, POSITION_PID, 2, 0.5, 0.01, 0, 0);
```

#### 3.3 快速扫描系统配置
```c
// step_motor_bsp.h 配置
#define MOTOR_MAX_SPEED     10            // 高速扫描
#define MOTOR_ACCEL         5             // 快速加速

// schedule.c 任务配置
static schedule_task_t schedule_task[] = {
    {uart_proc, 1, 0},      // 高频串口处理
    {pi_proc, 10, 0},       // 高频视觉处理
    {scan_proc, 50, 0}      // 扫描控制任务
};
```

## 常见问题与解决方案

### 1. 电机不响应
**可能原因**:
- 串口配置错误
- 电机地址不匹配
- 波特率设置错误

**解决方法**:
```c
// 检查串口配置
#define MOTOR_X_UART        huart2  // 确认串口号
#define MOTOR_X_ADDR        0x01    // 确认电机地址

// 添加调试信息
my_printf(&huart1, "Motor command sent: addr=%d\r\n", MOTOR_X_ADDR);
```

### 2. PID控制震荡
**可能原因**:
- PID参数不合适
- 采样频率过高/过低
- 系统延迟过大

**解决方法**:
```c
// 降低PID增益
PID_struct_init(&pid_x, POSITION_PID, 1, 0.1, 0.005, 0, 0);

// 添加死区
pid_x.input_deadband = 3;   // 输入死区
pid_x.output_deadband = 1;  // 输出死区

// 调整控制周期
{pi_proc, 50, 0}  // 从20ms改为50ms
```

### 3. 串口数据丢失
**可能原因**:
- 缓冲区太小
- 处理速度跟不上
- DMA配置问题

**解决方法**:
```c
// 增大缓冲区
uint8_t ringbuffer_pool_x[128];  // 从64增加到128

// 提高处理频率
{uart_proc, 1, 0}  // 保持1ms高频处理

// 检查DMA配置
// 在.ioc文件中确认DMA设置正确
```

---

**版权声明**: 本文档归属于【米醋电子工作室】
**文档版本**: v1.0
**最后更新**: 2025年1月
