# 绿色激光追踪固定红色激光修改指南

## 📋 任务目标
将原来的"绿色激光追踪移动的红色激光"改为"绿色激光追踪一个完全不动的固定红色激光"

## 🔍 当前系统分析

### 原始追踪逻辑
```c
// 当前的追踪代码 (bsp/pi_bsp.c 第63-64行)
pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
```

**解释**:
- `latest_green_laser_coord.x/y`: 绿色激光的当前位置 (实际值)
- `latest_red_laser_coord.x/y`: 红色激光的当前位置 (目标值)
- PID控制器让绿色激光位置跟随红色激光位置

### 问题分析
当前系统中，红色激光位置会实时更新，导致目标位置不断变化。我们需要将红色激光位置固定为一个不变的值。

## 🛠️ 详细修改步骤

### 第一步：定义固定的红色激光位置

**文件**: `bsp/pi_bsp.h`
**位置**: 在文件末尾添加固定目标位置定义

```c
#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// 激光类型标识符
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// 激光坐标数据结构
typedef struct {
    char type;    // 激光类型: 'R'表示红色激光，'G'表示绿色激光
    int x;        // X坐标
    int y;        // Y坐标
    uint8_t isValid; // 有效性指示：当前坐标是否有效/已更新
} LaserCoord_t;

// *** 新增：固定的红色激光目标位置 ***
#define FIXED_RED_LASER_X    320    // 固定红色激光X坐标 (屏幕中心)
#define FIXED_RED_LASER_Y    240    // 固定红色激光Y坐标 (屏幕中心)

int pi_parse_data(char *buffer);
void pi_proc(void);

extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;

#endif
```

**说明**:
- `FIXED_RED_LASER_X = 320`: 假设摄像头分辨率为640x480，中心点X坐标为320
- `FIXED_RED_LASER_Y = 240`: 假设摄像头分辨率为640x480，中心点Y坐标为240
- **您需要根据实际摄像头分辨率和期望的目标位置修改这两个值**

### 第二步：修改追踪逻辑

**文件**: `bsp/pi_bsp.c`
**位置**: 修改 `pi_proc()` 函数

**原始代码**:
```c
void pi_proc(void)
{
    float pos_out_x,pos_out_y=0;

    pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
    Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);
}
```

**修改后的代码**:
```c
void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;
    
    // *** 修改：使用固定的红色激光位置作为目标 ***
    // 不再使用 latest_red_laser_coord.x/y，改用固定值
    pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, FIXED_RED_LASER_X, 0);
    pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, FIXED_RED_LASER_Y, 0);
    
    // 电机控制输出保持不变
    Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
    
    // *** 可选：添加调试信息 ***
    // my_printf(&huart1, "Green:(%d,%d) Target:(%d,%d) Output:(%.2f,%.2f)\r\n", 
    //           latest_green_laser_coord.x, latest_green_laser_coord.y,
    //           FIXED_RED_LASER_X, FIXED_RED_LASER_Y,
    //           pos_out_x, pos_out_y);
}
```

### 第三步：优化红色激光数据处理（可选）

**文件**: `bsp/pi_bsp.c`
**位置**: 修改 `pi_parse_data()` 函数

由于我们不再需要红色激光的实时位置，可以选择忽略红色激光数据或者仅用于监控：

**选项A：完全忽略红色激光数据**
```c
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 空指针错误

    int parsed_x, parsed_y; // 临时变量，用于存储解析出的X,Y坐标
    int parsed_count;

    // 只处理绿色激光数据，忽略红色激光
    if (strncmp(buffer, "gre:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) // 必须解析出X和Y两个值
            return -2; // 解析失败

        // 解析成功，更新全局绿色激光坐标
        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1; // 标记数据为有效

        // 打印调试信息
        my_printf(&huart1, "Parsed GREEN: X=%d, Y=%d\r\n", 
                  latest_green_laser_coord.x, latest_green_laser_coord.y);
    }
    // *** 删除红色激光处理部分 ***
    else if (strncmp(buffer, "red:", 4) == 0)
    {
        // 忽略红色激光数据，或者仅用于调试
        my_printf(&huart1, "Red laser data ignored: %s\r\n", buffer);
        return 0; // 返回成功但不处理
    }
    else
    {
        // 既不是 "red:" 也不是 "gre:" 开头，认为是未知格式或无效数据
        return -3; // 未知或无效格式
    }

    return 0; // 成功
}
```

**选项B：保留红色激光数据用于监控**
```c
// 保持原有的 pi_parse_data() 函数不变
// 这样可以监控红色激光是否真的不动
```

### 第四步：调整PID参数（推荐）

**文件**: `app/mypid.c`
**位置**: `PID_INIT()` 函数

由于目标位置现在是固定的，可以调整PID参数以获得更好的控制效果：

```c
void PID_INIT(void)
{
    /********************pid参数************************/
    // *** 针对固定目标优化的PID参数 ***
    // 可以适当增加积分项，因为目标不再变化
    PID_struct_init(&pid_x, POSITION_PID, 5, 2, 0.05, 0, 0);  // 增加Kp和Ki
    PID_struct_init(&pid_y, POSITION_PID, 5, 2, 0.05, 0, 0);  // 增加Kp和Ki
    
    // *** 可选：添加死区，避免在目标附近震荡 ***
    pid_x.input_deadband = 5;   // X方向死区：5个像素
    pid_y.input_deadband = 5;   // Y方向死区：5个像素
    
    // 其他PID控制器保持不变
    PID_struct_init(&pid_speed_left, DELTA_PID, 200, 50, 0.5, 0.2, 0);
    PID_struct_init(&pid_speed_right, DELTA_PID, 200, 50, 0.5, 0.2, 0);
    PID_struct_init(&pid_location_left, POSITION_PID, 40, 0, 0.1, 0, 0);
    PID_struct_init(&pid_location_right, POSITION_PID, 40, 0, 0.1, 0, 0);
}
```

## 🧪 测试验证步骤

### 第一步：编译验证
1. 保存所有修改的文件
2. 在Keil MDK中编译项目
3. 确保没有编译错误

### 第二步：功能测试
1. 将程序下载到STM32
2. 连接串口调试工具（波特率根据UART1配置）
3. 观察调试输出信息

### 第三步：效果验证
1. **预期行为**: 绿色激光应该移动到固定位置(320, 240)并停止
2. **观察要点**:
   - 绿色激光是否向目标位置移动
   - 到达目标位置后是否稳定
   - 是否存在震荡现象

## ⚙️ 参数调整指南

### 目标位置调整
如果需要修改固定目标位置，只需修改 `pi_bsp.h` 中的定义：
```c
#define FIXED_RED_LASER_X    400    // 改为其他X坐标
#define FIXED_RED_LASER_Y    300    // 改为其他Y坐标
```

### PID参数调整
如果控制效果不理想，可以调整PID参数：

**响应太慢**:
```c
PID_struct_init(&pid_x, POSITION_PID, 8, 3, 0.1, 0, 0);  // 增加Kp和Ki
```

**有震荡**:
```c
PID_struct_init(&pid_x, POSITION_PID, 2, 0.5, 0.02, 0, 0);  // 减小Kp和Ki
pid_x.input_deadband = 10;  // 增加死区
```

**到达目标后有稳态误差**:
```c
PID_struct_init(&pid_x, POSITION_PID, 3, 2, 0.02, 0, 0);  // 增加Ki
```

## 🚨 注意事项

1. **坐标系统**: 确保固定目标坐标在摄像头视野范围内
2. **机械限制**: 确保电机能够到达目标位置而不超出机械限制
3. **调试输出**: 建议保留调试输出以便观察系统行为
4. **安全测试**: 首次测试时建议降低电机速度限制

## 📝 完整代码修改示例

### 修改文件1: `bsp/pi_bsp.h`
```c
#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// 激光类型标识符
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// *** 新增：固定红色激光目标位置配置 ***
// 根据您的摄像头分辨率和需求修改这些值
#define FIXED_RED_LASER_X    320    // 固定目标X坐标
#define FIXED_RED_LASER_Y    240    // 固定目标Y坐标

// 激光坐标数据结构
typedef struct {
    char type;       // 激光类型: 'R'表示红色激光，'G'表示绿色激光
    int x;          // X坐标
    int y;          // Y坐标
    uint8_t isValid; // 有效性指示：当前坐标是否有效/已更新
} LaserCoord_t;

// 函数声明
int pi_parse_data(char *buffer);
void pi_proc(void);

// 全局变量声明
extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;

#endif
```

### 修改文件2: `bsp/pi_bsp.c`
```c
#include "pi_bsp.h"

// 默认值设为无效状态，X, Y 为 0
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};
LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0};

// MaixCam 数据解析函数，输入格式：red:(x,y)\n 或 gre:(x,y)\n
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 空指针错误

    int parsed_x, parsed_y;
    int parsed_count;

    // 处理绿色激光数据 - 这是我们需要的实际位置
    if (strncmp(buffer, "gre:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2; // 解析失败

        // 更新绿色激光坐标
        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1;

        my_printf(&huart1, "GREEN: X=%d, Y=%d\r\n",
                  latest_green_laser_coord.x, latest_green_laser_coord.y);
    }
    // 处理红色激光数据 - 现在仅用于监控，不影响控制
    else if (strncmp(buffer, "red:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2;

        // 更新红色激光坐标（仅用于监控）
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1;

        my_printf(&huart1, "RED (ignored): X=%d, Y=%d\r\n",
                  latest_red_laser_coord.x, latest_red_laser_coord.y);
    }
    else
    {
        return -3; // 未知格式
    }

    return 0; // 成功
}

// *** 核心修改：追踪逻辑函数 ***
void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;

    // 检查绿色激光数据是否有效
    if (latest_green_laser_coord.isValid)
    {
        // *** 关键修改：使用固定目标位置而不是红色激光位置 ***
        pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, FIXED_RED_LASER_X, 0);
        pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, FIXED_RED_LASER_Y, 0);

        // 控制电机移动
        Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);

        // 调试输出：显示当前状态
        my_printf(&huart1, "Control: Green(%d,%d)->Target(%d,%d) Output(%.2f,%.2f)\r\n",
                  latest_green_laser_coord.x, latest_green_laser_coord.y,
                  FIXED_RED_LASER_X, FIXED_RED_LASER_Y,
                  pos_out_x, pos_out_y);
    }
    else
    {
        // 没有有效的绿色激光数据，停止电机
        Step_Motor_Set_Speed_my(0, 0);
        my_printf(&huart1, "No valid green laser data, motors stopped\r\n");
    }
}
```

## 🔧 高级配置选项

### 选项1: 多个固定目标位置
如果需要在多个固定位置之间切换：

```c
// 在 pi_bsp.h 中定义多个目标
#define TARGET_CENTER_X     320
#define TARGET_CENTER_Y     240
#define TARGET_TOP_LEFT_X   160
#define TARGET_TOP_LEFT_Y   120
#define TARGET_BOTTOM_RIGHT_X 480
#define TARGET_BOTTOM_RIGHT_Y 360

// 在 pi_bsp.c 中添加目标选择变量
static uint8_t current_target = 0;  // 0=中心, 1=左上, 2=右下

void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;
    int target_x, target_y;

    // 根据当前目标选择固定位置
    switch(current_target)
    {
        case 0:
            target_x = TARGET_CENTER_X;
            target_y = TARGET_CENTER_Y;
            break;
        case 1:
            target_x = TARGET_TOP_LEFT_X;
            target_y = TARGET_TOP_LEFT_Y;
            break;
        case 2:
            target_x = TARGET_BOTTOM_RIGHT_X;
            target_y = TARGET_BOTTOM_RIGHT_Y;
            break;
        default:
            target_x = TARGET_CENTER_X;
            target_y = TARGET_CENTER_Y;
            break;
    }

    if (latest_green_laser_coord.isValid)
    {
        pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, target_x, 0);
        pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, target_y, 0);
        Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
    }
}

// 目标切换函数（可通过串口命令调用）
void switch_target(uint8_t target_id)
{
    if (target_id <= 2)
    {
        current_target = target_id;
        my_printf(&huart1, "Target switched to %d\r\n", target_id);
    }
}
```

### 选项2: 动态目标位置设置
通过串口命令动态设置目标位置：

```c
// 在 pi_bsp.c 中添加全局变量
static int dynamic_target_x = FIXED_RED_LASER_X;
static int dynamic_target_y = FIXED_RED_LASER_Y;

// 目标位置设置函数
void set_target_position(int x, int y)
{
    // 边界检查（假设摄像头分辨率为640x480）
    if (x >= 0 && x <= 640 && y >= 0 && y <= 480)
    {
        dynamic_target_x = x;
        dynamic_target_y = y;
        my_printf(&huart1, "Target position set to (%d, %d)\r\n", x, y);
    }
    else
    {
        my_printf(&huart1, "Invalid target position (%d, %d)\r\n", x, y);
    }
}

// 修改 pi_proc 函数使用动态目标
void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;

    if (latest_green_laser_coord.isValid)
    {
        pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, dynamic_target_x, 0);
        pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, dynamic_target_y, 0);
        Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
    }
}
```

### 选项3: 到达目标后的行为控制
```c
// 在 pi_bsp.c 中添加状态变量
static uint8_t target_reached = 0;
static uint32_t stable_time = 0;

#define POSITION_TOLERANCE  5    // 位置容差（像素）
#define STABLE_TIME_MS     1000  // 稳定时间（毫秒）

void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;

    if (latest_green_laser_coord.isValid)
    {
        // 计算位置误差
        int error_x = abs(latest_green_laser_coord.x - FIXED_RED_LASER_X);
        int error_y = abs(latest_green_laser_coord.y - FIXED_RED_LASER_Y);

        // 检查是否到达目标
        if (error_x <= POSITION_TOLERANCE && error_y <= POSITION_TOLERANCE)
        {
            if (!target_reached)
            {
                target_reached = 1;
                stable_time = HAL_GetTick();
                my_printf(&huart1, "Target reached!\r\n");
            }

            // 到达目标后可以选择停止或保持微调
            pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, FIXED_RED_LASER_X, 0);
            pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, FIXED_RED_LASER_Y, 0);

            // 可选：到达目标一定时间后完全停止
            if (HAL_GetTick() - stable_time > STABLE_TIME_MS)
            {
                pos_out_x = 0;
                pos_out_y = 0;
                my_printf(&huart1, "Target stable, motors stopped\r\n");
            }
        }
        else
        {
            target_reached = 0;
            pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, FIXED_RED_LASER_X, 0);
            pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, FIXED_RED_LASER_Y, 0);
        }

        Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
    }
}
```

## 🐛 常见问题排查

### 问题1: 绿色激光不移动
**可能原因**:
- 没有接收到绿色激光数据
- PID参数过小
- 电机连接问题

**排查步骤**:
1. 检查串口调试输出是否有"GREEN: X=xxx, Y=xxx"
2. 检查是否有"Control: Green(...)..."输出
3. 临时增大PID参数测试

### 问题2: 绿色激光移动但不到目标位置
**可能原因**:
- 目标位置超出机械限制
- PID参数不合适
- 坐标系统不匹配

**排查步骤**:
1. 确认FIXED_RED_LASER_X/Y值在合理范围内
2. 检查电机是否有角度限制
3. 调整PID参数

### 问题3: 到达目标位置后震荡
**可能原因**:
- PID参数过大
- 没有设置死区
- 机械系统有间隙

**解决方法**:
```c
// 减小PID参数
PID_struct_init(&pid_x, POSITION_PID, 2, 0.5, 0.01, 0, 0);
// 增加死区
pid_x.input_deadband = 8;
pid_y.input_deadband = 8;
```

---

**版权声明**: 本文档归属于【米醋电子工作室】
**文档版本**: v1.0
**最后更新**: 2025年1月
